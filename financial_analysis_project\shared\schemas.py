"""
Shared data schemas for the financial analysis project.
These schemas are used by both backend and frontend components.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class SupplierSummary(BaseModel):
    """Summary of financial data for a supplier."""
    supplier_name: str
    total_gross_amount: float
    total_transactions: int
    average_margin: Optional[float] = None
    low_margin_transactions: int = 0
    negative_margin_transactions: int = 0


class VoucherSummary(BaseModel):
    """Summary of financial data for a voucher."""
    voucher_id: str
    supplier_name: str
    gross_amount: float
    cost_amount: Optional[float] = None
    margin_percentage: Optional[float] = None
    is_low_margin: bool = False
    is_negative_margin: bool = False
    transaction_date: Optional[datetime] = None


class MonthlyTrend(BaseModel):
    """Monthly profitability trend data."""
    year_month: str  # Format: "2024-01"
    supplier_name: str
    total_gross_amount: float
    total_cost: Optional[float] = None
    profit_margin: Optional[float] = None
    transaction_count: int


class FinancialAnalysisResult(BaseModel):
    """Complete financial analysis results."""
    analysis_timestamp: datetime = Field(default_factory=datetime.now)
    file_name: str
    total_records_processed: int
    
    # Summary statistics
    total_gross_amount: float
    total_suppliers: int
    total_vouchers: int
    
    # Detailed breakdowns
    supplier_summaries: List[SupplierSummary]
    voucher_summaries: List[VoucherSummary]
    monthly_trends: List[MonthlyTrend]
    
    # Highlighted issues
    low_margin_transactions: List[VoucherSummary]
    negative_margin_transactions: List[VoucherSummary]
    
    # Analysis metadata
    assumptions_used: List[str] = []
    warnings: List[str] = []
    processing_time_seconds: Optional[float] = None


class AnalysisRequest(BaseModel):
    """Request model for financial analysis."""
    file_name: str
    assume_cost_percentage: Optional[float] = Field(
        default=70.0, 
        description="Percentage of gross amount to assume as cost when cost data is missing"
    )
    low_margin_threshold: float = Field(
        default=10.0,
        description="Threshold percentage below which margins are considered low"
    )


class AnalysisResponse(BaseModel):
    """Response model for financial analysis API."""
    success: bool
    message: str
    data: Optional[FinancialAnalysisResult] = None
    error_details: Optional[Dict[str, Any]] = None
