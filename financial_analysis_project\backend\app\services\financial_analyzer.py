"""
Financial analysis service that orchestrates the Lang<PERSON>hain agents.
"""

import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime
import logging
import time

from ..agents.pandas_agents import run_financial_analysis
from .excel_processor import ExcelProcessor

# Import shared schemas
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from shared.schemas import (
    FinancialAnalysisResult, SupplierSummary, VoucherSummary,
    MonthlyTrend, AnalysisRequest
)

logger = logging.getLogger(__name__)


class FinancialAnalyzer:
    """Main service for financial analysis using LangChain agents."""
    
    def __init__(self):
        self.excel_processor = ExcelProcessor()
    
    def analyze_file(self, file_content: bytes, filename: str, 
                    config: Optional[Dict] = None) -> FinancialAnalysisResult:
        """
        Perform complete financial analysis on uploaded Excel file.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            config: Analysis configuration parameters
            
        Returns:
            FinancialAnalysisResult with complete analysis
        """
        start_time = time.time()
        
        try:
            # Process Excel file
            logger.info(f"Processing Excel file: {filename}")
            df = self.excel_processor.process_file(file_content, filename)
            
            # Set default configuration
            if config is None:
                config = {
                    "assume_cost_percentage": 70.0,
                    "low_margin_threshold": 10.0
                }
            
            # Run LangChain analysis workflow
            logger.info("Running financial analysis workflow")
            analysis_results = run_financial_analysis(df, config)
            
            # Extract structured data from analysis results
            structured_results = self._extract_structured_results(
                df, analysis_results, config
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create final result
            result = FinancialAnalysisResult(
                file_name=filename,
                total_records_processed=len(df),
                total_gross_amount=float(df['gross_amount'].sum()),
                total_suppliers=df['supplier'].nunique(),
                total_vouchers=df['voucher'].nunique(),
                supplier_summaries=structured_results['supplier_summaries'],
                voucher_summaries=structured_results['voucher_summaries'],
                monthly_trends=structured_results['monthly_trends'],
                low_margin_transactions=structured_results['low_margin_transactions'],
                negative_margin_transactions=structured_results['negative_margin_transactions'],
                assumptions_used=analysis_results['assumptions'],
                warnings=analysis_results['warnings'],
                processing_time_seconds=processing_time
            )
            
            logger.info(f"Analysis completed in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Error in financial analysis: {str(e)}")
            raise
    
    def _extract_structured_results(self, df: pd.DataFrame, 
                                   analysis_results: Dict, 
                                   config: Dict) -> Dict:
        """Extract structured data from LangChain analysis results."""
        
        # Calculate margins if cost data is available or assumed
        df_with_margins = self._calculate_margins(df, config['assume_cost_percentage'])
        
        # Generate supplier summaries
        supplier_summaries = self._generate_supplier_summaries(df_with_margins)
        
        # Generate voucher summaries
        voucher_summaries = self._generate_voucher_summaries(df_with_margins, config)
        
        # Generate monthly trends
        monthly_trends = self._generate_monthly_trends(df_with_margins)
        
        # Identify problematic transactions
        low_margin_threshold = config['low_margin_threshold']
        low_margin_transactions = [
            vs for vs in voucher_summaries 
            if vs.margin_percentage is not None and vs.margin_percentage < low_margin_threshold
        ]
        
        negative_margin_transactions = [
            vs for vs in voucher_summaries 
            if vs.margin_percentage is not None and vs.margin_percentage < 0
        ]
        
        return {
            'supplier_summaries': supplier_summaries,
            'voucher_summaries': voucher_summaries,
            'monthly_trends': monthly_trends,
            'low_margin_transactions': low_margin_transactions,
            'negative_margin_transactions': negative_margin_transactions
        }
    
    def _calculate_margins(self, df: pd.DataFrame, assume_cost_percentage: float) -> pd.DataFrame:
        """Calculate margins for all transactions."""
        df = df.copy()
        
        # Fill missing costs with assumed percentage
        if 'cost' in df.columns:
            df['cost'] = df['cost'].fillna(df['gross_amount'] * (assume_cost_percentage / 100))
        else:
            df['cost'] = df['gross_amount'] * (assume_cost_percentage / 100)
        
        # Calculate margin percentage
        df['margin_percentage'] = ((df['gross_amount'] - df['cost']) / df['gross_amount'] * 100).round(2)
        
        return df
    
    def _generate_supplier_summaries(self, df: pd.DataFrame) -> List[SupplierSummary]:
        """Generate supplier-level summaries."""
        supplier_groups = df.groupby('supplier').agg({
            'gross_amount': ['sum', 'count'],
            'margin_percentage': 'mean'
        }).round(2)
        
        supplier_groups.columns = ['total_gross_amount', 'total_transactions', 'average_margin']
        
        summaries = []
        for supplier_name, row in supplier_groups.iterrows():
            # Count low and negative margin transactions
            supplier_data = df[df['supplier'] == supplier_name]
            low_margin_count = (supplier_data['margin_percentage'] < 10).sum()
            negative_margin_count = (supplier_data['margin_percentage'] < 0).sum()
            
            summary = SupplierSummary(
                supplier_name=supplier_name,
                total_gross_amount=float(row['total_gross_amount']),
                total_transactions=int(row['total_transactions']),
                average_margin=float(row['average_margin']) if pd.notna(row['average_margin']) else None,
                low_margin_transactions=int(low_margin_count),
                negative_margin_transactions=int(negative_margin_count)
            )
            summaries.append(summary)
        
        # Sort by total gross amount descending
        summaries.sort(key=lambda x: x.total_gross_amount, reverse=True)
        return summaries
    
    def _generate_voucher_summaries(self, df: pd.DataFrame, config: Dict) -> List[VoucherSummary]:
        """Generate voucher-level summaries."""
        summaries = []
        low_margin_threshold = config['low_margin_threshold']
        
        for _, row in df.iterrows():
            margin = row.get('margin_percentage')
            
            summary = VoucherSummary(
                voucher_id=str(row['voucher']),
                supplier_name=str(row['supplier']),
                gross_amount=float(row['gross_amount']),
                cost_amount=float(row['cost']) if pd.notna(row.get('cost')) else None,
                margin_percentage=float(margin) if pd.notna(margin) else None,
                is_low_margin=margin < low_margin_threshold if pd.notna(margin) else False,
                is_negative_margin=margin < 0 if pd.notna(margin) else False,
                transaction_date=row.get('date') if pd.notna(row.get('date')) else None
            )
            summaries.append(summary)
        
        return summaries
    
    def _generate_monthly_trends(self, df: pd.DataFrame) -> List[MonthlyTrend]:
        """Generate monthly trend analysis."""
        if 'date' not in df.columns:
            return []
        
        # Create year-month column
        df['year_month'] = df['date'].dt.to_period('M').astype(str)
        
        # Group by supplier and month
        monthly_groups = df.groupby(['supplier', 'year_month']).agg({
            'gross_amount': 'sum',
            'cost': 'sum',
            'voucher': 'count'
        }).reset_index()
        
        trends = []
        for _, row in monthly_groups.iterrows():
            profit_margin = None
            if pd.notna(row['cost']) and row['gross_amount'] > 0:
                profit_margin = ((row['gross_amount'] - row['cost']) / row['gross_amount'] * 100)
            
            trend = MonthlyTrend(
                year_month=row['year_month'],
                supplier_name=row['supplier'],
                total_gross_amount=float(row['gross_amount']),
                total_cost=float(row['cost']) if pd.notna(row['cost']) else None,
                profit_margin=float(profit_margin) if profit_margin is not None else None,
                transaction_count=int(row['voucher'])
            )
            trends.append(trend)
        
        # Sort by year_month and supplier
        trends.sort(key=lambda x: (x.year_month, x.supplier_name))
        return trends
